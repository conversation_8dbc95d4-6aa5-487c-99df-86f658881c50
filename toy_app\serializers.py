from rest_framework import serializers
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework import serializers
from django.contrib.auth import password_validation
from .utils import send_email_verification_code
from .models import *

User = get_user_model()


# register
class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        data = super().validate(attrs)
        data.update({
            'user_id': self.user.id,
            'email': self.user.email,
            'user_code': self.user.user_code,
            'is_staff': self.user.is_staff,
        })
        return data


class RegisterSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)

    def validate_email(self, value):
        if User.objects.filter(email=value, is_active=True).exists():
            raise serializers.ValidationError("Email already taken")
        return value

    def create(self, validated_data):
        email = validated_data['email']
        password = validated_data['password']

        try:
            user = User.objects.get(email=email)

            # Пользователь не активен → обновим пароль, заново отправим код
            user.set_password(password)
            user.save()

        except User.DoesNotExist:
            # Новый пользователь
            user = User.objects.create_user(
                email=email,
                password=password,
                is_active=False
            )

        # Очистим предыдущие коды и отправим новый
        EmailVerificationCode.objects.filter(user=user).delete()
        send_email_verification_code(user)

        return user


class VerifyCodeSerializer(serializers.Serializer):
    email = serializers.EmailField()
    code = serializers.CharField(max_length=6)

    def validate(self, data):
        email = data['email']
        code = data['code']

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            raise serializers.ValidationError("Пользователь не найден")

        try:
            code_entry = EmailVerificationCode.objects.filter(
                user=user,
                code=code,
                is_used=False
            ).latest('created_at')
        except EmailVerificationCode.DoesNotExist:
            raise serializers.ValidationError("Неверный код")

        if code_entry.is_expired():
            raise serializers.ValidationError("Код истёк")

        # Активируем пользователя
        user.is_active = True
        user.save()

        code_entry.is_used = True
        code_entry.save()

        return data



"""
    For viewing email verifications all list
"""
class EmailVerificationCodeSerializer(serializers.ModelSerializer):
    user_email = serializers.ReadOnlyField(source='user.email')

    class Meta:
        model = EmailVerificationCode
        fields = ['id', 'user', 'user_email', 'code', 'created_at' , 'is_used']
        read_only_fields = ['created_at']



class ResendCodeSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate_email(self, value):
        try:
            user = User.objects.get(email=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("User with this email does not exist")

        if user.is_active:
            raise serializers.ValidationError("User already verified")

        self.context['user'] = user  # передаём user во view через контекст
        return value



# game image
class GameGalleryItemSerializer(serializers.ModelSerializer):
    file_url = serializers.SerializerMethodField()

    class Meta:
        model = GameGalleryItem
        fields = ['id', 'game', 'file', 'file_url', 'uploaded_at']
        read_only_fields = ['id', 'uploaded_at']

    def get_file_url(self, obj):
        """Безопасно возвращает URL файла"""
        return obj.get_file_url()


# game file
class GameFileSerializer(serializers.ModelSerializer):
    file_name = serializers.ReadOnlyField()
    file_size = serializers.ReadOnlyField()
    download_url = serializers.SerializerMethodField()

    class Meta:
        model = GameFile
        fields = [
            'id', 'game', 'file', 'file_name', 'file_size', 'platform',
            'version', 'description', 'is_active', 'download_url',
            'uploaded_at', 'updated_at'
        ]
        read_only_fields = ['id', 'file_name', 'file_size', 'uploaded_at', 'updated_at']

    def get_download_url(self, obj):
        """Возвращает URL для скачивания файла"""
        if obj.file:
            try:
                # Проверяем, существует ли файл
                if hasattr(obj.file, 'path'):
                    import os
                    if not os.path.exists(obj.file.path):
                        return None

                request = self.context.get('request')
                if request:
                    return request.build_absolute_uri(f'/api/game-files/{obj.id}/download/')
            except (ValueError, OSError):
                # Файл не существует или недоступен
                return None
        return None


class GameShortSerializer(serializers.ModelSerializer):
    class Meta:
        model = Game
        fields = ['id', 'title']  # добавь ещё поля, если нужно


class GameWithImageSerializer(serializers.ModelSerializer):
    """Serializer for games with image - used in available_games"""
    class Meta:
        model = Game
        fields = ['id', 'title', 'cover_image']


# game
class GameSerializer(serializers.ModelSerializer):
    is_in_cart = serializers.SerializerMethodField()
    is_in_library = serializers.SerializerMethodField()
    gallery_items = GameGalleryItemSerializer(many=True, read_only=True)
    has_access = serializers.SerializerMethodField()
    has_unactivated_access = serializers.SerializerMethodField()
    access_end = serializers.SerializerMethodField()
    game_files = serializers.SerializerMethodField()
    cover_image_url = serializers.SerializerMethodField()

    class Meta:
        model = Game
        fields = [
            'id',
            'title',
            'game_code',
            'subtitle',
            'description',
            'how_to_play',
            'target_audience',
            'requires_device',
            'price',
            'trial_available',
            'cover_image',
            'cover_image_url',
            'system_requirements',
            'required_equipment',
            'created_at',
            'is_in_cart',
            'is_in_library',
            'has_access',
            'access_end',
            'has_unactivated_access',
            'gallery_items',
            'game_files',
        ]
        read_only_fields = ['id', 'created_at']

    def get_is_in_cart(self, obj):
        user = self.context['request'].user
        if not user.is_authenticated:
            return False
        return CartItem.objects.filter(user=user, game=obj).exists()

    def get_is_in_library(self, obj):
        user = self.context['request'].user
        if not user.is_authenticated:
            return False
        return UserLibrary.objects.filter(user=user, game=obj).exists()

    def get_access_end(self, obj):
        user = self.context['request'].user
        if not user.is_authenticated:
            return None

        now = timezone.now()
        access = UserGameAccess.objects.filter(
            user=user,
            game=obj,
            access_start__lte=now,
            access_end__gte=now
        ).order_by('-access_end').first()

        if not access:
            return None

        return access.access_end

    def get_has_access(self, obj):
        user = self.context['request'].user
        if not user.is_authenticated:
            return False

        now = timezone.now()
        return UserGameAccess.objects.filter(
            user=user,
            game=obj,
            access_start__lte=now,
            access_end__gte=now
        ).exists()

    def get_has_unactivated_access(self, obj):
        user = self.context['request'].user
        if not user.is_authenticated:
            return False

        return UserGameAccess.objects.filter(
            user=user,
            game=obj,
            activated=False
        ).exists()

    def get_game_files(self, obj):
        user = self.context['request'].user
        if not user.is_authenticated:
            return []

        # Проверяем доступ к игре
        now = timezone.now()
        has_access = UserGameAccess.objects.filter(
            user=user,
            game=obj,
            access_start__lte=now,
            access_end__gte=now
        ).exists()

        # Если нет доступа, возвращаем пустой список
        if not has_access:
            return []

        # Если есть доступ, возвращаем активные файлы игры
        game_files = obj.game_files.filter(is_active=True)

        # Фильтруем файлы, которые действительно существуют
        valid_files = []
        for game_file in game_files:
            try:
                if game_file.file and hasattr(game_file.file, 'path'):
                    import os
                    if os.path.exists(game_file.file.path):
                        valid_files.append(game_file)
                elif game_file.file:  # Для облачного хранилища
                    valid_files.append(game_file)
            except (ValueError, OSError):
                # Файл не существует или недоступен
                continue

        return GameFileSerializer(valid_files, many=True, context=self.context).data

    def get_cover_image_url(self, obj):
        """Безопасно возвращает URL обложки игры"""
        return obj.get_cover_image_url()



"""
    Пакет игр
"""
class GamePackageSerializer(serializers.ModelSerializer):
    games = GameShortSerializer(many=True, read_only=True)
    game_ids = serializers.PrimaryKeyRelatedField(
        many=True, queryset=Game.objects.all(), write_only=True
    )
    has_active_subscription = serializers.SerializerMethodField()
    user_has_any_active_subscription = serializers.SerializerMethodField()

    class Meta:
        model = GamePackage
        fields = ['id', 'name', 'description', 'price', 'benefit_1', 'benefit_2', 'benefit_3',
                  'duration_days', 'games', 'game_ids', 'max_selectable_games', 'has_active_subscription', 'user_has_any_active_subscription']

    def get_has_active_subscription(self, obj):
        """Проверяет, есть ли у пользователя активная подписка на этот пакет"""
        request = self.context.get('request')
        if not request or not request.user or not request.user.is_authenticated:
            return False

        from django.utils import timezone
        from .models import UserPackageSubscription

        return UserPackageSubscription.objects.filter(
            user=request.user,
            package=obj,
            is_active=True,
            expires_at__gt=timezone.now()
        ).exists()

    def get_user_has_any_active_subscription(self, obj):
        """Проверяет, есть ли у пользователя активная подписка на любой пакет"""
        request = self.context.get('request')
        if not request or not request.user or not request.user.is_authenticated:
            return False

        from django.utils import timezone
        from .models import UserPackageSubscription

        return UserPackageSubscription.objects.filter(
            user=request.user,
            is_active=True,
            expires_at__gt=timezone.now()
        ).exists()

    def create(self, validated_data):
        games = validated_data.pop('game_ids')
        package = GamePackage.objects.create(**validated_data)
        package.games.set(games)
        return package




class CartItemSerializer(serializers.ModelSerializer):
    game = serializers.PrimaryKeyRelatedField(queryset=Game.objects.all())
    game_obj = GameSerializer(source='game', read_only=True)

    class Meta:
        model = CartItem
        fields = ['id', 'user', 'game', 'game_obj', 'quantity', 'added_at']
        read_only_fields = ['user', 'added_at']




    def validate(self, data):
        user = self.context['request'].user
        game = data.get('game')

        if not game:
            raise serializers.ValidationError("Игра обязательна.")

        if CartItem.objects.filter(user=user, game=game).exists():
            raise serializers.ValidationError("Игра уже в корзине.")

        now = timezone.now()
        if UserGameAccess.objects.filter(
            user=user,
            game=game,
            access_start__lte=now,
            access_end__gte=now,
            activated=True
        ).exists():
            raise serializers.ValidationError("У вас уже есть активный доступ к этой игре.")

        return data




# user detail serializer
class UserDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            'id',
            'email',
            'phone',
            'is_active',
            'is_staff',
            'is_superuser',
            'user_code',
            'date_joined',
            'last_login',
        ]
        read_only_fields = fields


"""
    Изменения данных для пользователя
"""
class UserSelfUpdateSerializer(serializers.ModelSerializer):
    active_subscriptions = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'email', 'phone', 'is_staff', 'user_code', 'active_subscriptions']
        read_only_fields = ['id', 'email', 'is_staff', 'user_code', 'active_subscriptions']

    def get_active_subscriptions(self, obj):
        """Возвращает активные подписки пользователя"""
        from django.utils import timezone

        active_subscriptions = UserPackageSubscription.objects.filter(
            user=obj,
            is_active=True,
            expires_at__gt=timezone.now()
        ).select_related('package').prefetch_related('selected_games')

        return UserPackageSubscriptionSerializer(active_subscriptions, many=True).data


"""
    Изменить пароль
"""
class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)

    def validate_new_password(self, value):
        password_validation.validate_password(value)
        return value

"""
    Полное изменение списка пользователей.
"""
class AdminUserUpdateSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = User
        fields = [
            'id',
            'email',
            'phone',
            'is_active',
            'is_staff',
            'is_superuser',
            'user_code',
            'date_joined',
            'last_login',
            'password',
        ]
        read_only_fields = ['date_joined', 'last_login']

    def update(self, instance, validated_data):
        validated_data.pop('is_superuser', None)
        password = validated_data.pop('password', None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        if password:
            instance.set_password(password)

        instance.save()
        return instance






# serializers.py
class UserLibrarySerializer(serializers.ModelSerializer):
    game = GameSerializer(read_only=True)
    access_end = serializers.SerializerMethodField()
    access_source_display = serializers.CharField(source='get_access_source_display', read_only=True)
    package_name = serializers.CharField(source='package_subscription.package.name', read_only=True)

    class Meta:
        model = UserLibrary
        fields = ['id', 'game', 'access_source', 'access_source_display', 'package_name', 'added_at', 'access_end']

    def get_access_end(self, obj):
        user = self.context['request'].user
        now = timezone.now()

        access = UserGameAccess.objects.filter(
            user=user,
            game=obj.game,
            access_start__lte=now,
            access_end__gte=now,
            activated=True
        ).order_by('-access_end').first()

        if not access:
            return None

        return access.access_end


# Добавление игры в библиотеку (ручная)
class AddToLibrarySerializer(serializers.ModelSerializer):
    user_id = serializers.IntegerField()
    game_id = serializers.IntegerField()

    class Meta:
        model = UserLibrary
        fields = ['user_id', 'game_id']

    def validate(self, attrs):
        user_id = attrs['user_id']
        game_id = attrs['game_id']

        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise serializers.ValidationError({'user_id': 'Пользователь не найден.'})

        try:
            game = Game.objects.get(id=game_id)
        except Game.DoesNotExist:
            raise serializers.ValidationError({'game_id': 'Игра не найдена.'})

        if UserLibrary.objects.filter(user=user, game=game).exists():
            raise serializers.ValidationError('Эта игра уже есть в библиотеке пользователя.')

        attrs['user'] = user
        attrs['game'] = game
        return attrs

    def create(self, validated_data):
        return UserLibrary.objects.create(
            user=validated_data['user'],
            game=validated_data['game']
        )


# Сериализатор для ключей игровых
class GameKeySerializer(serializers.ModelSerializer):
    game_title = serializers.CharField(source='game.title', read_only=True)
    user_email = serializers.EmailField(source='assigned_to_user.email', read_only=True)

    class Meta:
        model = GameKey
        fields = [
            'id',
            'game',
            'game_title',
            'code',
            'is_used',
            'assigned_to_user',
            'user_email',
            'assigned_at',
            'expires_at',
        ]
        read_only_fields = ['is_used', 'assigned_at', 'assigned_to_user']


"""
    Покупка для пользователя
"""


class PurchaseSerializer(serializers.ModelSerializer):
    game_title = serializers.CharField(source='game.title', read_only=True)
    package_title = serializers.CharField(source='package.name', read_only=True)

    class Meta:
        model = Purchase
        fields = ['id', 'game_title', 'purchase_type', 'package_title',
                  'price', 'status', 'created_at']


class UserGameAccessSerializer(serializers.ModelSerializer):
    has_access = serializers.SerializerMethodField()
    game_title = serializers.CharField(source='game.title', read_only=True)

    class Meta:
        model = UserGameAccess
        fields = ['id', 'game', 'game_title', 'access_type', 'access_start', 'access_end', 'has_access' , 'activated']

    def get_has_access(self, obj):
        return obj.has_access()



"""
    Для админки проверка доступов у игроков
"""
class AdminUserGameAccess(serializers.ModelSerializer):
    has_access = serializers.SerializerMethodField(read_only=True)
    user_email = serializers.EmailField(source='user.email', read_only=True)
    game_title = serializers.CharField(source='game.title', read_only=True)

    class Meta:
        model = UserGameAccess
        fields = [
            'id',
            'user',
            'user_email',
            'game',
            'game_title',
            'access_type',
            'access_start',
            'access_end',
            'has_access',
            'activated'
        ]

    def get_has_access(self, obj):
        return obj.has_access()


class UserPackageSubscriptionSerializer(serializers.ModelSerializer):
    package_name = serializers.CharField(source='package.name', read_only=True)
    package_description = serializers.CharField(source='package.description', read_only=True)
    selected_games = GameShortSerializer(many=True, read_only=True)
    available_games = serializers.SerializerMethodField()
    is_expired = serializers.ReadOnlyField()
    remaining_slots = serializers.SerializerMethodField()

    class Meta:
        model = UserPackageSubscription
        fields = [
            'id', 'package', 'package_name', 'package_description',
            'selected_games', 'available_games', 'games_selected_count',
            'is_active', 'activated_at', 'expires_at', 'is_expired',
            'remaining_slots'
        ]

    def get_available_games(self, obj):
        """Возвращает игры, доступные для выбора в пакете"""
        available_games = obj.package.games.exclude(
            id__in=obj.selected_games.values_list('id', flat=True)
        )
        return GameWithImageSerializer(available_games, many=True).data

    def get_remaining_slots(self, obj):
        return obj.remaining_game_slots()

